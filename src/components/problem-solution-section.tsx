"use client"

import { getSectionContent, ProblemSolutionContent } from "@/lib/content"

export function ProblemSolutionSection() {
  const content = getSectionContent(3) as ProblemSolutionContent

  return (
    <section className="py-16 px-8 md:px-16 lg:px-24 min-h-screen flex items-center" style={{ backgroundColor: 'var(--color-gray-50)' }}>
      <div className="max-w-6xl mx-auto w-full">
        <h2 className="text-3xl md:text-4xl font-bold text-center mb-16" style={{ color: 'var(--color-gray-900)' }}>
          {content.title}
        </h2>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 md:gap-12">
          {/* Problem */}
          <div className="text-center">
            <div className="problem-solution-card problem-card">
              <div className="icon-container mb-6">
                <div className="problem-icon">
                  <svg width="48" height="48" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M12 9v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                  </svg>
                </div>
              </div>
              <h3 className="text-2xl font-bold mb-4" style={{ color: 'var(--color-red-600)' }}>
                {content.labels.problemLabel}
              </h3>
              <p className="text-lg leading-relaxed" style={{ color: 'var(--color-gray-700)' }}>
                {content.problem}
              </p>
            </div>
          </div>

          {/* Solution */}
          <div className="text-center">
            <div className="problem-solution-card solution-card">
              <div className="icon-container mb-6">
                <div className="solution-icon">
                  <svg width="48" height="48" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                  </svg>
                </div>
              </div>
              <h3 className="text-2xl font-bold mb-4" style={{ color: 'var(--color-brand-blue-600)' }}>
                {content.labels.solutionLabel}
              </h3>
              <p className="text-lg leading-relaxed" style={{ color: 'var(--color-gray-700)' }}>
                {content.solution}
              </p>
            </div>
          </div>

          {/* Benefits */}
          <div className="text-center">
            <div className="problem-solution-card benefits-card">
              <div className="icon-container mb-6">
                <div className="benefits-icon">
                  <svg width="48" height="48" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M13 10V3L4 14h7v7l9-11h-7z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                  </svg>
                </div>
              </div>
              <h3 className="text-2xl font-bold mb-4" style={{ color: 'var(--color-green-600)' }}>
                {content.labels.benefitsLabel}
              </h3>
              <p className="text-lg leading-relaxed" style={{ color: 'var(--color-gray-700)' }}>
                {content.benefits}
              </p>
            </div>
          </div>
        </div>
      </div>
      
      <style jsx>{`
        .problem-solution-card {
          padding: 2.5rem 2rem;
          border-radius: 1rem;
          background: var(--color-white);
          box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
          transition: all 0.3s ease;
          height: 100%;
          border: 2px solid transparent;
        }
        
        .problem-solution-card:hover {
          transform: translateY(-4px);
          box-shadow: 0 12px 25px rgba(0, 0, 0, 0.1);
        }
        
        .problem-card:hover {
          border-color: var(--color-red-200);
        }
        
        .solution-card:hover {
          border-color: var(--color-brand-blue-200);
        }
        
        .benefits-card:hover {
          border-color: var(--color-green-200);
        }
        
        .icon-container {
          display: flex;
          justify-content: center;
          align-items: center;
        }
        
        .problem-icon {
          width: 80px;
          height: 80px;
          border-radius: 50%;
          background: linear-gradient(135deg, var(--color-red-100), var(--color-red-200));
          display: flex;
          align-items: center;
          justify-content: center;
          color: var(--color-red-600);
        }
        
        .solution-icon {
          width: 80px;
          height: 80px;
          border-radius: 50%;
          background: linear-gradient(135deg, var(--color-brand-blue-100), var(--color-brand-blue-200));
          display: flex;
          align-items: center;
          justify-content: center;
          color: var(--color-brand-blue-600);
        }
        
        .benefits-icon {
          width: 80px;
          height: 80px;
          border-radius: 50%;
          background: linear-gradient(135deg, var(--color-green-100), var(--color-green-200));
          display: flex;
          align-items: center;
          justify-content: center;
          color: var(--color-green-600);
        }
        
        @media (max-width: 768px) {
          .problem-solution-card {
            padding: 2rem 1.5rem;
          }
          
          .problem-icon,
          .solution-icon,
          .benefits-icon {
            width: 60px;
            height: 60px;
          }
        }
      `}</style>
    </section>
  )
}
